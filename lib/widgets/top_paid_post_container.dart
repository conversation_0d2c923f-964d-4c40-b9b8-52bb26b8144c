import 'package:flutter/material.dart';
import 'package:money_mouthy_two/services/post_service.dart';
import 'package:money_mouthy_two/services/user_service.dart';
import 'package:url_launcher/url_launcher.dart';

class TopPaidPostContainer extends StatefulWidget {
  final String category;
  final Post? topPost;
  final VoidCallback? onTap;

  const TopPaidPostContainer({
    super.key,
    required this.category,
    this.topPost,
    this.onTap,
  });

  @override
  State<TopPaidPostContainer> createState() => _TopPaidPostContainerState();
}

class _TopPaidPostContainerState extends State<TopPaidPostContainer> {
  final UserService _userService = UserService();
  String? _userProfileImage;
  String _userDisplayName = '';
  bool _isLoadingUserData = true;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void didUpdateWidget(TopPaidPostContainer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.topPost?.authorId != widget.topPost?.authorId) {
      _loadUserData();
    }
  }

  Future<void> _loadUserData() async {
    if (widget.topPost == null) {
      setState(() => _isLoadingUserData = false);
      return;
    }

    try {
      final userData = await _userService.getUserData(widget.topPost!.authorId);
      if (mounted) {
        setState(() {
          _userProfileImage =
              userData?['profileImageUrl'] ?? userData?['photoUrl'];
          _userDisplayName =
              userData?['username'] ??
              userData?['name'] ??
              widget.topPost!.author;
          _isLoadingUserData = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading user data: $e');
      if (mounted) {
        setState(() {
          _userDisplayName = widget.topPost!.author;
          _isLoadingUserData = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.topPost == null) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Top Ranked Category Header
                Row(
                  children: [
                    _isLoadingUserData
                        ? Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            shape: BoxShape.circle,
                          ),
                          child: const Center(
                            child: SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          ),
                        )
                        : CircleAvatar(
                          radius: 20,
                          backgroundColor: Colors.grey[200],
                          backgroundImage:
                              _userProfileImage != null
                                  ? NetworkImage(_userProfileImage!)
                                  : null,
                          child:
                              _userProfileImage == null
                                  ? Text(
                                    _userDisplayName.isNotEmpty
                                        ? _userDisplayName[0].toUpperCase()
                                        : 'A',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontWeight: FontWeight.bold,
                                    ),
                                  )
                                  : null,
                        ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(
                                Icons.emoji_events,
                                color: Color(0xFFFFD700),
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              const Text(
                                'Top Ranked Category',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black87,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          // Category indicator bars
                          Row(
                            children: [
                              Container(
                                width: 40,
                                height: 3,
                                decoration: BoxDecoration(
                                  color: Colors.green,
                                  borderRadius: BorderRadius.circular(2),
                                ),
                              ),
                              const SizedBox(width: 2),
                              Container(
                                width: 30,
                                height: 3,
                                decoration: BoxDecoration(
                                  color: Colors.orange,
                                  borderRadius: BorderRadius.circular(2),
                                ),
                              ),
                              const SizedBox(width: 2),
                              Container(
                                width: 20,
                                height: 3,
                                decoration: BoxDecoration(
                                  color: Colors.red,
                                  borderRadius: BorderRadius.circular(2),
                                ),
                              ),
                              const SizedBox(width: 2),
                              Container(
                                width: 15,
                                height: 3,
                                decoration: BoxDecoration(
                                  color: Colors.purple,
                                  borderRadius: BorderRadius.circular(2),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        widget.topPost!.formattedPrice,
                        style: const TextStyle(
                          color: Colors.black87,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),
                const Divider(height: 1, color: Colors.grey),
                const SizedBox(height: 16),

                // Post content section
                _buildPostContentSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPostContentSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // User info and post details
        Row(
          children: [
            _isLoadingUserData
                ? Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    shape: BoxShape.circle,
                  ),
                  child: const Center(
                    child: SizedBox(
                      width: 12,
                      height: 12,
                      child: CircularProgressIndicator(strokeWidth: 1.5),
                    ),
                  ),
                )
                : CircleAvatar(
                  radius: 16,
                  backgroundColor: Colors.grey[200],
                  backgroundImage:
                      _userProfileImage != null
                          ? NetworkImage(_userProfileImage!)
                          : null,
                  child:
                      _userProfileImage == null
                          ? Text(
                            _userDisplayName.isNotEmpty
                                ? _userDisplayName[0].toUpperCase()
                                : 'A',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          )
                          : null,
                ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _isLoadingUserData ? 'Loading...' : _userDisplayName,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  Text(
                    widget.topPost!.timeAgo,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: const Color(0xFF4285F4),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'Paid Post',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    widget.topPost!.formattedPrice,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // Post title
        Text(
          widget.topPost!.content.split('\n').first,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
            height: 1.3,
          ),
        ),

        const SizedBox(height: 8),

        // Post content
        Text(
          widget.topPost!.content.length > 200
              ? '${widget.topPost!.content.substring(0, 200)}...'
              : widget.topPost!.content,
          style: TextStyle(fontSize: 14, color: Colors.grey[700], height: 1.4),
        ),

        // Media content
        // if (_hasMediaContent()) ...[
        //   const SizedBox(height: 12),
        //   _buildMediaContent(),
        // ],
        const SizedBox(height: 12),

        // // Action buttons
        // Row(
        //   children: [
        //     _buildActionButton(Icons.link, ''),
        //     const SizedBox(width: 16),
        //     _buildActionButton(Icons.repeat, widget.topPost!.likes.toString()),
        //     const SizedBox(width: 16),
        //     _buildActionButton(Icons.share, ''),
        //     const Spacer(),
        //     const Icon(Icons.more_horiz, color: Colors.grey),
        //   ],
        // ),
      ],
    );
  }

  Widget _buildActionButton(IconData icon, String count) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: Colors.grey[600], size: 18),
        if (count.isNotEmpty) ...[
          const SizedBox(width: 4),
          Text(count, style: TextStyle(color: Colors.grey[600], fontSize: 12)),
        ],
      ],
    );
  }

  bool _hasMediaContent() {
    if (widget.topPost == null) return false;
    return widget.topPost!.imageUrls.isNotEmpty ||
        widget.topPost!.videoUrls.isNotEmpty ||
        (widget.topPost!.linkUrl != null &&
            widget.topPost!.linkUrl!.isNotEmpty);
  }

  Widget _buildMediaContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Images in horizontal layout like reference design
        if (widget.topPost!.imageUrls.isNotEmpty) _buildImageGallery(),

        // Videos
        if (widget.topPost!.videoUrls.isNotEmpty) ...[
          if (widget.topPost!.imageUrls.isNotEmpty) const SizedBox(height: 8),
          _buildVideoSection(),
        ],

        // URL Link
        if (widget.topPost!.linkUrl != null &&
            widget.topPost!.linkUrl!.isNotEmpty) ...[
          if (widget.topPost!.imageUrls.isNotEmpty ||
              widget.topPost!.videoUrls.isNotEmpty)
            const SizedBox(height: 8),
          _buildLinkPreview(),
        ],
      ],
    );
  }

  Widget _buildImageGallery() {
    final images = widget.topPost!.imageUrls;
    if (images.isEmpty) return const SizedBox.shrink();

    // Match reference design with side-by-side images
    if (images.length == 1) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Image.network(
          images[0],
          height: 200,
          width: double.infinity,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              height: 200,
              color: Colors.grey[300],
              child: const Icon(Icons.image_not_supported, color: Colors.grey),
            );
          },
        ),
      );
    }

    // For multiple images, show them side by side like in reference
    return Row(
      children: [
        Expanded(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.network(
              images[0],
              height: 150,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: 150,
                  color: Colors.grey[300],
                  child: const Icon(
                    Icons.image_not_supported,
                    color: Colors.grey,
                  ),
                );
              },
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.network(
              images.length > 1 ? images[1] : images[0],
              height: 150,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: 150,
                  color: Colors.grey[300],
                  child: const Icon(
                    Icons.image_not_supported,
                    color: Colors.grey,
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildVideoSection() {
    final videos = widget.topPost!.videoUrls;
    if (videos.isEmpty) return const SizedBox.shrink();

    return SizedBox(
      height: 60,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: videos.length,
        itemBuilder: (context, index) {
          return Container(
            width: 100,
            margin: EdgeInsets.only(right: index < videos.length - 1 ? 8 : 0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.white.withValues(alpha: 0.1),
            ),
            child: InkWell(
              onTap: () => _launchUrl(videos[index]),
              borderRadius: BorderRadius.circular(8),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.play_circle_filled,
                      color: Colors.white,
                      size: 24,
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Video',
                      style: TextStyle(color: Colors.white, fontSize: 10),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLinkPreview() {
    final linkUrl = widget.topPost!.linkUrl;
    if (linkUrl == null || linkUrl.isEmpty) return const SizedBox.shrink();

    return InkWell(
      onTap: () => _launchUrl(linkUrl),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.white.withValues(alpha: 0.1),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            const Icon(Icons.link, color: Colors.white, size: 16),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                linkUrl.length > 40
                    ? '${linkUrl.substring(0, 40)}...'
                    : linkUrl,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  decoration: TextDecoration.underline,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const Icon(Icons.open_in_new, color: Colors.white, size: 14),
          ],
        ),
      ),
    );
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
    }
  }
}
