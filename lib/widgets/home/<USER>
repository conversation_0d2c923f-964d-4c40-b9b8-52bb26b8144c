import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:money_mouthy_two/services/follow_service.dart';
import 'package:money_mouthy_two/services/post_service.dart';
import 'package:money_mouthy_two/widgets/post_card.dart';
import 'package:money_mouthy_two/widgets/skeleton_loader.dart';
import 'package:money_mouthy_two/screens/post_detail_screen.dart';

/// Following Tab Widget with Real-time Updates
class FollowingTab extends StatefulWidget {
  const FollowingTab({super.key});

  @override
  State<FollowingTab> createState() => _FollowingTabState();
}

class _FollowingTabState extends State<FollowingTab> {
  final FollowService _followService = FollowService();
  final PostService _postService = PostService();
  final ScrollController _scrollController = ScrollController();

  List<Post> _followingPosts = [];
  List<Post> _allFollowingPosts = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _hasMorePosts = true;

  static const int _postsPerPage = 10;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _initializeAndSetupUpdates();
  }

  Future<void> _initializeAndSetupUpdates() async {
    try {
      // Ensure PostService is initialized before setting up streams
      await _postService.initialized;
      _setupRealTimeUpdates();
    } catch (e) {
      debugPrint('Error initializing Following tab: $e');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _setupRealTimeUpdates() {
    // Listen to both following changes and posts changes for real-time updates
    _followService.getFollowingStream().listen(
      (followingUserIds) {
        _updateFollowingPosts(followingUserIds);
      },
      onError: (error) {
        debugPrint('Error in following stream: $error');
        if (mounted) {
          setState(() => _isLoading = false);
        }
      },
    );

    _postService.postsStream.listen(
      (allPosts) {
        // Get current following list and update posts
        _followService
            .getFollowingStream()
            .first
            .then((followingUserIds) {
              _updateFollowingPosts(followingUserIds, allPosts);
            })
            .catchError((error) {
              debugPrint('Error getting following list: $error');
              if (mounted) {
                setState(() => _isLoading = false);
              }
            });
      },
      onError: (error) {
        debugPrint('Error in posts stream: $error');
        if (mounted) {
          setState(() => _isLoading = false);
        }
      },
    );
  }

  void _updateFollowingPosts(
    List<String> followingUserIds, [
    List<Post>? allPosts,
  ]) {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    _updateFollowingPostsAsync(followingUserIds, allPosts);
  }

  Future<void> _updateFollowingPostsAsync(
    List<String> followingUserIds, [
    List<Post>? allPosts,
  ]) async {
    try {
      if (followingUserIds.isEmpty) {
        if (mounted) {
          setState(() {
            _followingPosts = [];
            _allFollowingPosts = [];
            _isLoading = false;
            _hasMorePosts = false;
          });
        }
        return;
      }

      // Use provided posts or get from service (ensuring initialization)
      final posts = allPosts ?? await _postService.getAllPostsAsync();
      final followingPosts =
          posts
              .where((post) => followingUserIds.contains(post.authorId))
              .toList();

      // Sort by creation date (newest first)
      followingPosts.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      if (mounted) {
        setState(() {
          _allFollowingPosts = followingPosts;
          _currentPage = 0; // Reset pagination
          _followingPosts = _getPaginatedPosts();
          _hasMorePosts = _followingPosts.length < _allFollowingPosts.length;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error updating following posts: $e');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoadingMore && _hasMorePosts) {
        _loadMorePosts();
      }
    }
  }

  Future<void> _loadMorePosts() async {
    if (_isLoadingMore || !_hasMorePosts) return;

    setState(() => _isLoadingMore = true);

    // Simulate network delay for smooth UX
    await Future.delayed(const Duration(milliseconds: 500));

    setState(() {
      _currentPage++;
      final newPosts = _getPaginatedPosts();
      _followingPosts = newPosts;
      _hasMorePosts = _followingPosts.length < _allFollowingPosts.length;
      _isLoadingMore = false;
    });
  }

  List<Post> _getPaginatedPosts() {
    final endIndex = (_currentPage + 1) * _postsPerPage;
    return _allFollowingPosts
        .take(endIndex.clamp(0, _allFollowingPosts.length))
        .toList();
  }

  Future<void> _refreshFollowingPosts() async {
    try {
      // Force refresh by getting current following list and updating posts
      final followingUserIds = await _followService.getFollowingStream().first;
      await _updateFollowingPostsAsync(followingUserIds);
    } catch (e) {
      debugPrint('Error refreshing following posts: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading && _followingPosts.isEmpty) {
      return ListView.builder(
        padding: const EdgeInsets.fromLTRB(12, 4, 12, 80),
        itemCount: 5,
        itemBuilder: (context, index) => const PostCardSkeleton(),
      );
    }

    if (_followingPosts.isEmpty && !_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No posts from followed users',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Follow other users to see their posts here',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pushNamed(context, '/connect');
              },
              icon: const Icon(Icons.person_add),
              label: const Text('Find People to Follow'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF5159FF),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        HapticFeedback.mediumImpact();
        await _refreshFollowingPosts();
      },
      color: const Color(0xFF4285F4),
      backgroundColor: Colors.white,
      strokeWidth: 3,
      displacement: 60,
      child: ListView.builder(
        controller: _scrollController,
        physics: const BouncingScrollPhysics(
          parent: AlwaysScrollableScrollPhysics(),
        ),
        padding: const EdgeInsets.fromLTRB(12, 4, 12, 80),
        itemCount: _followingPosts.length + (_hasMorePosts ? 1 : 0),
        itemBuilder: (context, index) {
          // Show loading indicator at the end
          if (index == _followingPosts.length) {
            return _buildLoadingIndicator();
          }

          return PostCard(
            post: _followingPosts[index],
            onLike: () => _handleLike(_followingPosts[index]),
            onPurchase: () => _handlePurchase(_followingPosts[index]),
            onView: () => _handleView(_followingPosts[index]),
            onTap: () => _handlePostTap(_followingPosts[index]),
          );
        },
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child:
            _isLoadingMore
                ? Column(
                  key: const ValueKey('loading'),
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TweenAnimationBuilder<double>(
                      duration: const Duration(milliseconds: 1200),
                      tween: Tween(begin: 0.0, end: 1.0),
                      builder: (context, value, child) {
                        return Transform.rotate(
                          angle: value * 2 * 3.14159,
                          child: const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Color(0xFF4285F4),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 8),
                    AnimatedOpacity(
                      duration: const Duration(milliseconds: 600),
                      opacity: 0.7,
                      child: const Text(
                        'Loading more posts...',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                )
                : const SizedBox.shrink(key: ValueKey('empty')),
      ),
    );
  }

  void _handleLike(Post post) {
    _postService.likePost(post.id);
  }

  void _handlePurchase(Post post) {
    // Handle purchase functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Purchase functionality for ${post.formattedPrice}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _handleView(Post post) {
    _postService.viewPost(post.id);
  }

  void _handlePostTap(Post post) {
    // Navigate to post detail screen
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => PostDetailScreen(post: post)),
    );
  }
}
