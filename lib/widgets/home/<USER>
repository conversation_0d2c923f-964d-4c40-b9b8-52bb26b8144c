import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:money_mouthy_two/widgets/profile_drawer.dart';
import 'package:money_mouthy_two/services/wallet_service.dart';
import 'package:money_mouthy_two/services/post_service.dart';

import 'home_app_bar.dart';
import 'home_tab_bar.dart';
import 'explore_tab.dart';
import 'following_tab.dart';
import 'category_data.dart';

/// Home Content Widget - Contains the main home screen logic without bottom navigation
class HomeContent extends StatefulWidget {
  const HomeContent({super.key});

  @override
  State<HomeContent> createState() => _HomeContentState();
}

class _HomeContentState extends State<HomeContent>
    with TickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late TabController _tabController;
  int currentCategoryIndex = 0;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeServices() async {
    try {
      await WalletService().initialize();
      await PostService().initialize();
      if (mounted) {
        setState(() {
          // Refresh UI after services are ready
        });
      }
    } catch (e) {
      debugPrint('Error initializing services: $e');
    }
  }

  void _onCategorySelected(int index) {
    setState(() {
      currentCategoryIndex = index;
    });
  }

  /// Reset category to Politics (used by main navigation)
  void resetToPolitics() {
    setState(() {
      final politicsIndex = Categories.all.indexWhere(
        (cat) => cat.name == 'Politics',
      );
      if (politicsIndex != -1) {
        currentCategoryIndex = politicsIndex;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isWideScreen = constraints.maxWidth >= 768;

        return Scaffold(
          key: _scaffoldKey,
          backgroundColor: Colors.white,
          drawer: !isWideScreen ? ProfileDrawer() : null,
          appBar: HomeAppBar(scaffoldKey: _scaffoldKey),
          body: _buildWideScreenLayout(),
        );
      },
    );
  }

  Widget _buildWideScreenLayout() {
    return Center(
      child: Container(
        constraints: BoxConstraints(maxWidth: kIsWeb ? 800 : double.infinity),
        child: Row(
          children: [
            // Permanent sidebar for wide screens
            Container(
              width: kIsWeb ? 300 : 200,
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                border: Border(right: BorderSide(color: Colors.grey.shade200)),
              ),
              child: ProfileDrawer(),
            ),
            // Main content
            Expanded(
              child: Container(
                constraints: const BoxConstraints(maxWidth: 900),
                child: _buildMainContent(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return _buildMainContent();
  }

  Widget _buildMainContent() {
    return Column(
      children: [
        HomeTabBar(tabController: _tabController),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              ExploreTab(
                currentCategoryIndex: currentCategoryIndex,
                categories: Categories.all,
                onCategorySelected: _onCategorySelected,
              ),
              const FollowingTab(),
            ],
          ),
        ),
      ],
    );
  }
}
