import 'package:flutter/material.dart';

/// Category data model
class CategoryData {
  final String name;
  final Color color;
  final double topPrice;

  const CategoryData({
    required this.name,
    required this.color,
    required this.topPrice,
  });
}

/// Predefined categories
class Categories {
  static const List<CategoryData> all = [
    CategoryData(name: 'News', color: Color(0xFF29CC76), topPrice: 0.0),
    CategoryData(name: 'Politics', color: Color(0xFF4C5DFF), topPrice: 0.0),
    CategoryData(name: 'Sex', color: Color(0xFFFF4081), topPrice: 0.0),
    CategoryData(
      name: 'Entertainment',
      color: Color(0xFFA06A00),
      topPrice: 0.0,
    ),
    CategoryData(name: 'Sports', color: Color(0xFFC43DFF), topPrice: 0.0),
    CategoryData(name: 'Religion', color: Color(0xFF000000), topPrice: 0.0),
  ];
}

/// Category Badge Widget
class CategoryBadge extends StatelessWidget {
  final CategoryData category;

  const CategoryBadge({super.key, required this.category});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: category.color,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        category.name,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
