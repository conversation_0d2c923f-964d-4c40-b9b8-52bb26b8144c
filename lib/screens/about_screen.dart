import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: const Color(0xFF0f172a),
        title: const Text(
          'About Money Mouthy',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          return Center(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: kIsWeb ? 800 : double.infinity,
              ),
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(32),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // App Logo/Icon
                    Center(
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          // color: const Color(0xFF5159FF),
                          borderRadius: BorderRadius.circular(24),
                        ),

                        // Put logo here like on top of app
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(24),
                          child: Image.asset(
                            'assets/images/money_mouth.png',
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 32),

                    // App Name and Version
                    const Center(
                      child: Column(
                        children: [
                          Text(
                            'Money Mouthy',
                            style: TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'Version 1.0.0',
                            style: TextStyle(fontSize: 16, color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 32),

                    // About Description
                    const Text(
                      'About Money Mouthy',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Money Mouthy is a revolutionary microblogging platform where your voice has value. Share your opinions, engage with others, and get rewarded for meaningful content across six dynamic categories: News, Politics, Sports, Entertainment, Sex, and Religion.',
                      style: TextStyle(
                        fontSize: 16,
                        height: 1.6,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Key Features
                    const Text(
                      'Key Features',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildFeatureItem(
                      '💰',
                      'Monetized Posts',
                      'Share your thoughts for a minimum of \$0.05',
                    ),
                    _buildFeatureItem(
                      '🏆',
                      'Top Rankings',
                      'Highest paid posts stay at the top for 24 hours',
                    ),
                    _buildFeatureItem(
                      '📱',
                      'Rich Media',
                      'Share text, images, videos, and links',
                    ),
                    _buildFeatureItem(
                      '🎯',
                      'Six Categories',
                      'Focused discussions in specialized topics',
                    ),
                    _buildFeatureItem(
                      '👥',
                      'Social Features',
                      'Follow, connect, and engage with others',
                    ),
                    const SizedBox(height: 32),

                    // Company Info
                    const Text(
                      'Company Information',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Money Mouthy is developed and operated by Cuptoopia.com, Inc.',
                      style: TextStyle(
                        fontSize: 16,
                        height: 1.6,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 32),

                    // Quick Links Section
                    const Text(
                      'More Information',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Quick Links Grid
                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: constraints.maxWidth > 600 ? 2 : 1,
                      childAspectRatio: constraints.maxWidth > 600 ? 3.5 : 4.5,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      children: [
                        _buildQuickLinkCard(
                          context,
                          'Contact Us',
                          'Get in touch with our team',
                          Icons.email_outlined,
                          () => Navigator.pushNamed(context, '/contact'),
                        ),
                        _buildQuickLinkCard(
                          context,
                          'Support',
                          'Get help and support',
                          Icons.help_outline,
                          () => Navigator.pushNamed(context, '/support'),
                        ),
                        _buildQuickLinkCard(
                          context,
                          'Privacy Policy',
                          'Learn how we protect your data',
                          Icons.privacy_tip_outlined,
                          () => Navigator.pushNamed(context, '/privacy'),
                        ),
                        _buildQuickLinkCard(
                          context,
                          'Terms of Service',
                          'Read our terms and conditions',
                          Icons.description_outlined,
                          () => Navigator.pushNamed(context, '/terms'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 32),

                    // Copyright
                    const Center(
                      child: Text(
                        '© 2025 Cuptoopia.com, Inc. All rights reserved.',
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildFeatureItem(String emoji, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(emoji, style: const TextStyle(fontSize: 20)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(fontSize: 14, color: Colors.black87),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickLinkCard(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(4),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: const Color(0xFF5159FF).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: const Color(0xFF5159FF), size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
            ],
          ),
        ),
      ),
    );
  }
}
