import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/domain/entities.dart';
import '../../../auth/application/auth_providers.dart';
import '../../application/wallet_providers.dart';
import '../../domain/entities/transaction.dart';
import '../widgets/wallet_balance_card.dart';
import '../widgets/wallet_stats_cards.dart';
import '../widgets/transaction_list_view.dart';
import '../widgets/add_funds_dialog.dart';
import '../widgets/withdraw_funds_dialog.dart';

/// New wallet screen using Clean Architecture with Riverpod
class WalletScreenV2 extends ConsumerStatefulWidget {
  const WalletScreenV2({super.key});

  @override
  ConsumerState<WalletScreenV2> createState() => _WalletScreenV2State();
}

class _WalletScreenV2State extends ConsumerState<WalletScreenV2>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // Initialize wallet when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeWallet();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _initializeWallet() {
    final currentUser = ref.read(currentUserProvider);
    if (currentUser != null) {
      ref.read(walletNotifierProvider.notifier).initializeWallet(currentUser.id);
    }
  }

  void _showAddFundsDialog() {
    showDialog(
      context: context,
      builder: (context) => const AddFundsDialog(),
    );
  }

  void _showWithdrawDialog() {
    showDialog(
      context: context,
      builder: (context) => const WithdrawFundsDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final walletState = ref.watch(walletStateProvider);
    final isLoading = ref.watch(isWalletLoadingProvider);
    final hasError = ref.watch(hasWalletErrorProvider);
    final error = ref.watch(walletErrorProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Wallet',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.green.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.read(walletNotifierProvider.notifier).refreshWallet();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Balance Card
          const WalletBalanceCard(),

          // Stats Cards
          const WalletStatsCards(),

          // Error Display
          if (hasError && error != null)
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red.shade600),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      error.userMessage,
                      style: TextStyle(color: Colors.red.shade700),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      ref.read(walletNotifierProvider.notifier).clearError();
                    },
                    child: const Text('Dismiss'),
                  ),
                ],
              ),
            ),

          // Tabs
          TabBar(
            controller: _tabController,
            labelColor: Colors.green.shade600,
            unselectedLabelColor: Colors.grey.shade600,
            indicatorColor: Colors.green.shade600,
            tabs: const [
              Tab(text: 'All'),
              Tab(text: 'Income'),
              Tab(text: 'Expenses'),
            ],
          ),

          // Transaction List
          Expanded(
            child: isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                    controller: _tabController,
                    children: [
                      TransactionListView(
                        filter: TransactionFilter.all,
                        onLoadMore: () {
                          ref.read(walletNotifierProvider.notifier)
                              .loadMoreTransactions();
                        },
                      ),
                      TransactionListView(
                        filter: TransactionFilter.income,
                        onLoadMore: () {
                          ref.read(walletNotifierProvider.notifier)
                              .loadMoreTransactions();
                        },
                      ),
                      TransactionListView(
                        filter: TransactionFilter.expenses,
                        onLoadMore: () {
                          ref.read(walletNotifierProvider.notifier)
                              .loadMoreTransactions();
                        },
                      ),
                    ],
                  ),
          ),
        ],
      ),
      floatingActionButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton.extended(
            onPressed: _showAddFundsDialog,
            backgroundColor: Colors.green.shade600,
            foregroundColor: Colors.white,
            icon: const Icon(Icons.add),
            label: const Text('ReUp!'),
            heroTag: 'add_funds',
          ),
          const SizedBox(height: 8),
          FloatingActionButton.extended(
            onPressed: _showWithdrawDialog,
            backgroundColor: Colors.orange.shade600,
            foregroundColor: Colors.white,
            icon: const Icon(Icons.remove),
            label: const Text('Withdraw'),
            heroTag: 'withdraw',
          ),
        ],
      ),
    );
  }
}

/// Transaction filter enum for different tabs
enum TransactionFilter {
  all,
  income,
  expenses,
}
