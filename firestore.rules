rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    function isSignedIn() {
      return request.auth != null;
    }
    function isVerified() {
      return isSignedIn() && request.auth.token.email_verified == true;
    }

    // Each user can read/write their own document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // usernames uniqueness registry
    match /usernames/{uname} {
      // Anyone logged in can read to perform availability checks
      allow get, list: if request.auth != null;
      // Create only if document does not yet exist and the uid matches
      allow create: if request.auth != null &&
                    !exists(/databases/$(database)/documents/usernames/$(uname)) &&
                    request.resource.data.uid == request.auth.uid;
      // Allow owning user to delete (e.g., account deletion)
      allow delete: if request.auth != null && resource.data.uid == request.auth.uid;
    }

    // Other collections (posts, wallets, etc.)
    match /{document=**} {
      allow read, write: if isVerified();
    }
  }
} 