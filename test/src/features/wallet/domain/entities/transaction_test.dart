import 'package:flutter_test/flutter_test.dart';
import 'package:money_mouthy_two/src/core/domain/entities.dart';
import 'package:money_mouthy_two/src/features/wallet/domain/entities/transaction.dart';

void main() {
  group('Transaction Entity Tests', () {
    late Transaction testTransaction;
    late Money testAmount;

    setUp(() {
      testAmount = Money(amount: 100.0, currency: 'USD');
      testTransaction = Transaction(
        id: 'test-transaction-id',
        userId: 'test-user-id',
        amount: testAmount,
        type: TransactionType.credit,
        status: TransactionStatus.completed,
        description: 'Test transaction',
        createdAt: DateTime(2024, 1, 15, 10, 30),
        updatedAt: DateTime(2024, 1, 15, 10, 35),
      );
    });

    group('Transaction Creation', () {
      test('should create transaction with required fields', () {
        expect(testTransaction.id, equals('test-transaction-id'));
        expect(testTransaction.userId, equals('test-user-id'));
        expect(testTransaction.amount, equals(testAmount));
        expect(testTransaction.type, equals(TransactionType.credit));
        expect(testTransaction.status, equals(TransactionStatus.completed));
        expect(testTransaction.description, equals('Test transaction'));
      });

      test('should create transaction with default values', () {
        final transaction = Transaction(
          id: 'test-id',
          userId: 'user-id',
          amount: testAmount,
          type: TransactionType.debit,
          status: TransactionStatus.pending,
          description: 'Test',
          createdAt: DateTime.now(),
        );

        expect(transaction.metadata, isNull);
        expect(transaction.paymentMethodId, isNull);
        expect(transaction.relatedTransactionId, isNull);
        expect(transaction.postId, isNull);
        expect(transaction.failureReason, isNull);
      });
    });

    group('Transaction Status Checks', () {
      test('isCompleted should return true for completed status', () {
        expect(testTransaction.isCompleted, isTrue);
      });

      test('isPending should return true for pending status', () {
        final pendingTransaction = testTransaction.copyWith(
          status: TransactionStatus.pending,
        );
        expect(pendingTransaction.isPending, isTrue);
        expect(pendingTransaction.isCompleted, isFalse);
      });

      test('isFailed should return true for failed status', () {
        final failedTransaction = testTransaction.copyWith(
          status: TransactionStatus.failed,
        );
        expect(failedTransaction.isFailed, isTrue);
        expect(failedTransaction.isCompleted, isFalse);
      });

      test('isCancelled should return true for cancelled status', () {
        final cancelledTransaction = testTransaction.copyWith(
          status: TransactionStatus.cancelled,
        );
        expect(cancelledTransaction.isCancelled, isTrue);
        expect(cancelledTransaction.isCompleted, isFalse);
      });

      test('isProcessing should return true for processing status', () {
        final processingTransaction = testTransaction.copyWith(
          status: TransactionStatus.processing,
        );
        expect(processingTransaction.isProcessing, isTrue);
        expect(processingTransaction.isCompleted, isFalse);
      });
    });

    group('Transaction Type Checks', () {
      test('isCredit should return true for credit transactions', () {
        expect(testTransaction.isCredit, isTrue);
        expect(testTransaction.isDebit, isFalse);
      });

      test('isDebit should return true for debit transactions', () {
        final debitTransaction = testTransaction.copyWith(
          type: TransactionType.debit,
        );
        expect(debitTransaction.isDebit, isTrue);
        expect(debitTransaction.isCredit, isFalse);
      });

      test('isWithdrawal should return true for withdrawal transactions', () {
        final withdrawalTransaction = testTransaction.copyWith(
          type: TransactionType.withdrawal,
        );
        expect(withdrawalTransaction.isWithdrawal, isTrue);
        expect(withdrawalTransaction.isCredit, isFalse);
      });

      test('isPostPayment should return true for post payment transactions', () {
        final postPaymentTransaction = testTransaction.copyWith(
          type: TransactionType.postPayment,
        );
        expect(postPaymentTransaction.isPostPayment, isTrue);
        expect(postPaymentTransaction.isCredit, isFalse);
      });
    });

    group('Transaction Display Properties', () {
      test('displayTypeName should return correct type names', () {
        expect(testTransaction.displayTypeName, equals('Credit'));
        
        final debitTransaction = testTransaction.copyWith(type: TransactionType.debit);
        expect(debitTransaction.displayTypeName, equals('Debit'));
        
        final withdrawalTransaction = testTransaction.copyWith(type: TransactionType.withdrawal);
        expect(withdrawalTransaction.displayTypeName, equals('Withdrawal'));
        
        final postPaymentTransaction = testTransaction.copyWith(type: TransactionType.postPayment);
        expect(postPaymentTransaction.displayTypeName, equals('Post Payment'));
        
        final refundTransaction = testTransaction.copyWith(type: TransactionType.refund);
        expect(refundTransaction.displayTypeName, equals('Refund'));
        
        final feeTransaction = testTransaction.copyWith(type: TransactionType.fee);
        expect(feeTransaction.displayTypeName, equals('Fee'));
      });

      test('displayStatusName should return correct status names', () {
        expect(testTransaction.displayStatusName, equals('Completed'));
        
        final pendingTransaction = testTransaction.copyWith(status: TransactionStatus.pending);
        expect(pendingTransaction.displayStatusName, equals('Pending'));
        
        final failedTransaction = testTransaction.copyWith(status: TransactionStatus.failed);
        expect(failedTransaction.displayStatusName, equals('Failed'));
        
        final cancelledTransaction = testTransaction.copyWith(status: TransactionStatus.cancelled);
        expect(cancelledTransaction.displayStatusName, equals('Cancelled'));
        
        final processingTransaction = testTransaction.copyWith(status: TransactionStatus.processing);
        expect(processingTransaction.displayStatusName, equals('Processing'));
      });

      test('timeDisplay should return correct time format', () {
        final now = DateTime.now();
        
        // Recent transaction (less than 1 hour)
        final recentTransaction = testTransaction.copyWith(
          createdAt: now.subtract(const Duration(minutes: 30)),
        );
        expect(recentTransaction.timeDisplay, contains('30m ago'));
        
        // Transaction from today (less than 24 hours)
        final todayTransaction = testTransaction.copyWith(
          createdAt: now.subtract(const Duration(hours: 5)),
        );
        expect(todayTransaction.timeDisplay, contains('5h ago'));
        
        // Transaction from yesterday
        final yesterdayTransaction = testTransaction.copyWith(
          createdAt: now.subtract(const Duration(days: 1)),
        );
        expect(yesterdayTransaction.timeDisplay, contains('1d ago'));
      });
    });

    group('Transaction Business Logic', () {
      test('effectiveAmount should return positive amount for credit', () {
        expect(testTransaction.effectiveAmount, equals(testAmount));
      });

      test('effectiveAmount should return negative amount for debit', () {
        final debitTransaction = testTransaction.copyWith(
          type: TransactionType.debit,
        );
        expect(debitTransaction.effectiveAmount, equals(Money(amount: -100.0, currency: 'USD')));
      });

      test('canBeCancelled should return true for pending transactions', () {
        final pendingTransaction = testTransaction.copyWith(
          status: TransactionStatus.pending,
        );
        expect(pendingTransaction.canBeCancelled, isTrue);
      });

      test('canBeCancelled should return false for completed transactions', () {
        expect(testTransaction.canBeCancelled, isFalse);
      });

      test('canBeRefunded should return true for completed credit transactions', () {
        expect(testTransaction.canBeRefunded, isTrue);
      });

      test('canBeRefunded should return false for debit transactions', () {
        final debitTransaction = testTransaction.copyWith(
          type: TransactionType.debit,
        );
        expect(debitTransaction.canBeRefunded, isFalse);
      });

      test('isRecent should return true for transactions within 24 hours', () {
        final recentTransaction = testTransaction.copyWith(
          createdAt: DateTime.now().subtract(const Duration(hours: 12)),
        );
        expect(recentTransaction.isRecent, isTrue);
      });

      test('isRecent should return false for old transactions', () {
        final oldTransaction = testTransaction.copyWith(
          createdAt: DateTime.now().subtract(const Duration(days: 2)),
        );
        expect(oldTransaction.isRecent, isFalse);
      });

      test('age should return correct duration', () {
        final transactionTime = DateTime.now().subtract(const Duration(hours: 5));
        final transaction = testTransaction.copyWith(createdAt: transactionTime);
        
        final age = transaction.age;
        expect(age.inHours, equals(5));
      });
    });

    group('Transaction Validation', () {
      test('should validate transaction with all required fields', () {
        expect(testTransaction.isValid, isTrue);
      });

      test('should be invalid with empty description', () {
        final invalidTransaction = testTransaction.copyWith(description: '');
        expect(invalidTransaction.isValid, isFalse);
      });

      test('should be invalid with zero amount', () {
        final invalidTransaction = testTransaction.copyWith(
          amount: Money(amount: 0.0, currency: 'USD'),
        );
        expect(invalidTransaction.isValid, isFalse);
      });

      test('should be invalid with negative amount', () {
        final invalidTransaction = testTransaction.copyWith(
          amount: Money(amount: -50.0, currency: 'USD'),
        );
        expect(invalidTransaction.isValid, isFalse);
      });
    });

    group('Transaction Equality', () {
      test('should be equal when all properties match', () {
        final transaction1 = Transaction(
          id: 'test-id',
          userId: 'user-id',
          amount: Money(amount: 100.0),
          type: TransactionType.credit,
          status: TransactionStatus.completed,
          description: 'Test',
          createdAt: DateTime(2024, 1, 15),
        );

        final transaction2 = Transaction(
          id: 'test-id',
          userId: 'user-id',
          amount: Money(amount: 100.0),
          type: TransactionType.credit,
          status: TransactionStatus.completed,
          description: 'Test',
          createdAt: DateTime(2024, 1, 15),
        );

        expect(transaction1, equals(transaction2));
        expect(transaction1.hashCode, equals(transaction2.hashCode));
      });

      test('should not be equal when properties differ', () {
        final transaction1 = testTransaction;
        final transaction2 = testTransaction.copyWith(amount: Money(amount: 200.0));

        expect(transaction1, isNot(equals(transaction2)));
      });
    });
  });
}
